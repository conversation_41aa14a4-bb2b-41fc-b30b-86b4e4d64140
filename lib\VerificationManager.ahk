; ===============================================================================
; VerificationManager.ahk
; Universal verification system for WinCBT-Biometric
; Handles mode-agnostic verification logic for pre-exam, post-exam, and future verify modes
; ===============================================================================

class VerificationManager {

    ; Verification modes
    static MODE_PRE_EXAM := "pre"
    static MODE_POST_EXAM := "post"
    static MODE_VERIFY := "verify"  ; Future mode for verify vs post-exam

    ; Verification types
    static TYPE_PHOTO := "Photo"
    static TYPE_FINGERPRINT := "Fingerprint"
    static TYPE_RIGHT_FINGERPRINT := "RightFingerprint"
    static TYPE_SIGNATURE := "Signature"

    ; Verification methods
    static METHOD_AUTO := "Auto"
    static METHOD_MANUAL := "Manual"

    /**
     * Constructor
     */
    __New() {
        this.currentMode := VerificationManager.MODE_PRE_EXAM
        this.logPrefix := "VerificationManager"
    }

    /**
     * Set the current verification mode
     * @param {String} mode - The verification mode (pre, post, verify)
     */
    SetMode(mode) {
        this.currentMode := mode
        this.LogMessage("INFO", "Verification mode set to: " mode)
    }

    /**
     * Get the current verification mode
     * @return {String} The current verification mode
     */
    GetMode() {
        return this.currentMode
    }

    /**
     * Universal photo verification function
     * @param {String} rollNumber - The candidate's roll number
     * @param {String} capturedPhotoPath - Path to the captured photo
     * @param {String} verificationMode - Auto, Manual, or Both
     * @param {Integer} confidenceThreshold - Minimum confidence for auto-verification
     * @return {Object} Verification result with confidence, method, and status
     */
    VerifyPhoto(rollNumber, capturedPhotoPath, verificationMode := "Auto", confidenceThreshold := 85) {
        this.LogMessage("INFO", "Starting photo verification for candidate: " rollNumber " in mode: " this.currentMode)

        ; Determine reference photo based on current mode
        referencePhotoPath := this.GetReferencePhotoPath(rollNumber)

        ; Check if files exist
        if (!FileExist(referencePhotoPath)) {
            result := {
                success: false,
                confidence: 0,
                method: "Error",
                message: "Reference photo not found: " referencePhotoPath,
                referenceUsed: referencePhotoPath
            }
            this.LogMessage("ERROR", "Reference photo not found: " referencePhotoPath)
            return result
        }

        if (!FileExist(capturedPhotoPath)) {
            result := {
                success: false,
                confidence: 0,
                method: "Error",
                message: "Captured photo not found: " capturedPhotoPath,
                referenceUsed: referencePhotoPath
            }
            this.LogMessage("ERROR", "Captured photo not found: " capturedPhotoPath)
            return result
        }

        ; Perform verification using the biometric function
        verifyResult := VerifyPhoto(referencePhotoPath, capturedPhotoPath)

        ; Determine verification method and final result
        method := ""
        success := false

        if (verificationMode == "Manual") {
            ; Manual mode - always approve
            method := VerificationManager.METHOD_MANUAL
            success := true
            this.LogMessage("INFO", "Photo verification: Manual mode - auto-approved")
        } else {
            ; Auto or Both mode
            if (verifyResult.result && verifyResult.confidence >= confidenceThreshold) {
                method := VerificationManager.METHOD_AUTO
                success := true
                this.LogMessage("INFO", "Photo verification: Auto-approved with " verifyResult.confidence "% confidence")
            } else if (verificationMode == "Both") {
                ; In Both mode, approve even if below threshold
                method := VerificationManager.METHOD_MANUAL
                success := true
                this.LogMessage("INFO", "Photo verification: Both mode - manual approval despite low confidence (" verifyResult.confidence "%)")
            } else {
                ; Auto mode with low confidence - fail
                method := VerificationManager.METHOD_AUTO
                success := false
                this.LogMessage("INFO", "Photo verification: Auto-rejected with " verifyResult.confidence "% confidence (below " confidenceThreshold "% threshold)")
            }
        }

        ; Create comprehensive result object
        result := {
            success: success,
            confidence: Integer(verifyResult.confidence),
            method: method,
            message: verifyResult.message,
            referenceUsed: referencePhotoPath,
            capturedPath: capturedPhotoPath,
            mode: this.currentMode,
            timestamp: FormatTime(, "yyyy-MM-dd HH:mm:ss")
        }

        ; Log detailed verification result
        this.LogVerificationResult(VerificationManager.TYPE_PHOTO, rollNumber, result)

        return result
    }

    /**
     * Get the appropriate reference photo path based on current mode
     * @param {String} rollNumber - The candidate's roll number
     * @return {String} Path to the reference photo
     */
    GetReferencePhotoPath(rollNumber) {
        candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages")

        switch this.currentMode {
            case VerificationManager.MODE_PRE_EXAM:
                ; Pre-exam mode: compare against registered photo
                return PathManager.GetCandidatePhotoPath(rollNumber)

            case VerificationManager.MODE_POST_EXAM:
                ; Post-exam mode: compare against pre-exam captured photo
                return candidatesImgPath rollNumber "_captured_photo_pre.jpg"

            case VerificationManager.MODE_VERIFY:
                ; Future verify mode: compare against post-exam captured photo
                return candidatesImgPath rollNumber "_captured_photo_post.jpg"

            default:
                ; Default to registered photo
                return PathManager.GetCandidatePhotoPath(rollNumber)
        }
    }

    /**
     * Save captured photo with appropriate naming convention
     * @param {String} rollNumber - The candidate's roll number
     * @param {String} sourcePath - Path to the source photo file
     * @return {Object} Save result with success status and saved path
     */
    SaveCapturedPhoto(rollNumber, sourcePath) {
        try {
            candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages", true)

            ; Generate filename based on current mode
            filename := rollNumber "_captured_photo_" this.currentMode ".jpg"
            savePath := candidatesImgPath filename

            ; Copy the file
            FileCopy(sourcePath, savePath, true)

            this.LogMessage("INFO", "Saved captured photo: " savePath)

            return {
                success: true,
                savedPath: savePath,
                filename: filename
            }
        } catch Error as e {
            this.LogMessage("ERROR", "Error saving captured photo: " e.Message)
            return {
                success: false,
                savedPath: "",
                filename: "",
                error: e.Message
            }
        }
    }

    /**
     * Update verification status in database
     * @param {String} rollNumber - The candidate's roll number
     * @param {String} verificationType - Type of verification (Photo, Fingerprint, etc.)
     * @param {String} status - The status to set (Verified, Failed, etc.)
     * @param {Object} verificationData - Additional verification data (confidence, method, etc.)
     * @return {Boolean} Success status
     */
    UpdateVerificationStatus(rollNumber, verificationType, status, verificationData := "") {
        try {
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")

            ; Determine field name based on mode and verification type
            fieldName := this.GetStatusFieldName(verificationType)

            ; Write the status
            IniWrite(status, candidatesPath, rollNumber, fieldName)

            ; Store additional verification data if provided
            if (IsObject(verificationData)) {
                ; Store confidence level
                if (verificationData.HasOwnProp("confidence")) {
                    confidenceField := fieldName "Confidence"
                    IniWrite(verificationData.confidence, candidatesPath, rollNumber, confidenceField)
                }

                ; Store verification method
                if (verificationData.HasOwnProp("method")) {
                    methodField := fieldName "Method"
                    IniWrite(verificationData.method, candidatesPath, rollNumber, methodField)
                }

                ; Store verification timestamp
                if (verificationData.HasOwnProp("timestamp")) {
                    timestampField := fieldName "Timestamp"
                    IniWrite(verificationData.timestamp, candidatesPath, rollNumber, timestampField)
                }
            }

            this.LogMessage("INFO", "Updated " fieldName " to " status " for candidate " rollNumber)
            return true

        } catch Error as e {
            this.LogMessage("ERROR", "Error updating verification status: " e.Message)
            return false
        }
    }

    /**
     * Get the appropriate status field name based on mode and verification type
     * @param {String} verificationType - Type of verification
     * @return {String} The database field name
     */
    GetStatusFieldName(verificationType) {
        switch this.currentMode {
            case VerificationManager.MODE_PRE_EXAM:
                return verificationType "Status"

            case VerificationManager.MODE_POST_EXAM:
                return "PostExam" verificationType "Status"

            case VerificationManager.MODE_VERIFY:
                return "Verify" verificationType "Status"

            default:
                return verificationType "Status"
        }
    }

    /**
     * Log verification result with detailed information
     * @param {String} verificationType - Type of verification
     * @param {String} rollNumber - The candidate's roll number
     * @param {Object} result - Verification result object
     */
    LogVerificationResult(verificationType, rollNumber, result) {
        logMessage := "VERIFICATION RESULT - "
        logMessage .= "Type: " verificationType ", "
        logMessage .= "Candidate: " rollNumber ", "
        logMessage .= "Mode: " result.mode ", "
        logMessage .= "Success: " (result.success ? "YES" : "NO") ", "
        logMessage .= "Confidence: " result.confidence "%, "
        logMessage .= "Method: " result.method ", "
        logMessage .= "Reference: " result.referenceUsed

        this.LogMessage("INFO", logMessage)

        ; Also log to application log file
        try {
            detailedLog := FormatTime(, "yyyy-MM-dd HH:mm:ss") " - " logMessage "`n"
            FileAppend(detailedLog, "logs\verification.log")
        } catch {
            ; Ignore file append errors
        }
    }

    /**
     * Universal fingerprint verification function
     * @param {String} rollNumber - The candidate's roll number
     * @param {String} capturedTemplatePath - Path to the captured fingerprint template
     * @param {String} hand - "left" or "right"
     * @param {String} fingerprintMode - "save" or "compare"
     * @param {Integer} confidenceThreshold - Minimum confidence for auto-verification
     * @return {Object} Verification result with confidence, method, and status
     */
    VerifyFingerprint(rollNumber, capturedTemplatePath, hand := "left", fingerprintMode := "compare", confidenceThreshold := 85) {
        this.LogMessage("INFO", "Starting " hand " fingerprint verification for candidate: " rollNumber " in mode: " this.currentMode)

        ; In save mode, just save the template
        if (fingerprintMode == "save") {
            return this.SaveFingerprintTemplate(rollNumber, capturedTemplatePath, hand)
        }

        ; In compare mode, verify against existing template
        referenceTemplatePath := this.GetReferenceFingerprintPath(rollNumber, hand)

        ; Check if files exist
        if (!FileExist(referenceTemplatePath)) {
            result := {
                success: false,
                confidence: 0,
                method: "Error",
                message: "Reference fingerprint template not found: " referenceTemplatePath,
                referenceUsed: referenceTemplatePath
            }
            this.LogMessage("ERROR", "Reference fingerprint template not found: " referenceTemplatePath)
            return result
        }

        if (!FileExist(capturedTemplatePath)) {
            result := {
                success: false,
                confidence: 0,
                method: "Error",
                message: "Captured fingerprint template not found: " capturedTemplatePath,
                referenceUsed: referenceTemplatePath
            }
            this.LogMessage("ERROR", "Captured fingerprint template not found: " capturedTemplatePath)
            return result
        }

        ; Perform verification using the biometric function
        verifyResult := VerifyFingerprint(referenceTemplatePath, capturedTemplatePath)

        ; Determine verification method and final result
        method := VerificationManager.METHOD_AUTO
        success := verifyResult.result && verifyResult.confidence >= confidenceThreshold

        ; Create comprehensive result object
        result := {
            success: success,
            confidence: Integer(verifyResult.confidence),
            method: method,
            message: verifyResult.message,
            referenceUsed: referenceTemplatePath,
            capturedPath: capturedTemplatePath,
            mode: this.currentMode,
            hand: hand,
            timestamp: FormatTime(, "yyyy-MM-dd HH:mm:ss")
        }

        ; Log detailed verification result
        verificationType := (hand == "right") ? VerificationManager.TYPE_RIGHT_FINGERPRINT : VerificationManager.TYPE_FINGERPRINT
        this.LogVerificationResult(verificationType, rollNumber, result)

        return result
    }

    /**
     * Get the appropriate reference fingerprint path based on current mode
     * @param {String} rollNumber - The candidate's roll number
     * @param {String} hand - "left" or "right"
     * @return {String} Path to the reference fingerprint template
     */
    GetReferenceFingerprintPath(rollNumber, hand := "left") {
        fptPath := PathManager.GetDatabaseSubPath("Fingerprints")

        switch this.currentMode {
            case VerificationManager.MODE_PRE_EXAM:
                ; Pre-exam mode: compare against registered fingerprint (if exists)
                ; For now, use pre-exam captured as reference since registered may not exist
                return fptPath rollNumber "_captured_fingerprint_" hand "_pre.fpt"

            case VerificationManager.MODE_POST_EXAM:
                ; Post-exam mode: compare against pre-exam captured fingerprint
                return fptPath rollNumber "_captured_fingerprint_" hand "_pre.fpt"

            case VerificationManager.MODE_VERIFY:
                ; Future verify mode: compare against post-exam captured fingerprint
                return fptPath rollNumber "_captured_fingerprint_" hand "_post.fpt"

            default:
                ; Default to pre-exam captured
                return fptPath rollNumber "_captured_fingerprint_" hand "_pre.fpt"
        }
    }

    /**
     * Save fingerprint template with appropriate naming convention
     * @param {String} rollNumber - The candidate's roll number
     * @param {String} sourcePath - Path to the source template file
     * @param {String} hand - "left" or "right"
     * @return {Object} Save result with success status and saved path
     */
    SaveFingerprintTemplate(rollNumber, sourcePath, hand := "left") {
        try {
            fptPath := PathManager.GetDatabaseSubPath("Fingerprints", true)

            ; Generate filename based on current mode
            filename := rollNumber "_captured_fingerprint_" hand "_" this.currentMode ".fpt"
            savePath := fptPath filename

            ; Copy the file
            FileCopy(sourcePath, savePath, true)

            this.LogMessage("INFO", "Saved " hand " fingerprint template: " savePath)

            return {
                success: true,
                savedPath: savePath,
                filename: filename,
                confidence: 100,  ; Saved templates are considered 100% confidence
                method: "Save",
                message: "Fingerprint template saved successfully",
                mode: this.currentMode,
                hand: hand,
                timestamp: FormatTime(, "yyyy-MM-dd HH:mm:ss")
            }
        } catch Error as e {
            this.LogMessage("ERROR", "Error saving " hand " fingerprint template: " e.Message)
            return {
                success: false,
                savedPath: "",
                filename: "",
                error: e.Message,
                confidence: 0,
                method: "Error",
                message: "Error saving fingerprint template: " e.Message
            }
        }
    }

    /**
     * Universal signature verification function
     * @param {String} rollNumber - The candidate's roll number
     * @param {String} capturedSignaturePath - Path to the captured signature
     * @param {String} verificationMode - Auto, Manual, or Both
     * @param {Integer} confidenceThreshold - Minimum confidence for auto-verification
     * @return {Object} Verification result with confidence, method, and status
     */
    VerifySignature(rollNumber, capturedSignaturePath, verificationMode := "Auto", confidenceThreshold := 85) {
        this.LogMessage("INFO", "Starting signature verification for candidate: " rollNumber " in mode: " this.currentMode)

        ; Determine reference signature based on current mode
        referenceSignaturePath := this.GetReferenceSignaturePath(rollNumber)

        ; Check if files exist
        if (!FileExist(referenceSignaturePath)) {
            result := {
                success: false,
                confidence: 0,
                method: "Error",
                message: "Reference signature not found: " referenceSignaturePath,
                referenceUsed: referenceSignaturePath
            }
            this.LogMessage("ERROR", "Reference signature not found: " referenceSignaturePath)
            return result
        }

        if (!FileExist(capturedSignaturePath)) {
            result := {
                success: false,
                confidence: 0,
                method: "Error",
                message: "Captured signature not found: " capturedSignaturePath,
                referenceUsed: referenceSignaturePath
            }
            this.LogMessage("ERROR", "Captured signature not found: " capturedSignaturePath)
            return result
        }

        ; Perform verification using the biometric function
        verifyResult := VerifySignature(referenceSignaturePath, capturedSignaturePath)

        ; Determine verification method and final result
        method := ""
        success := false

        if (verificationMode == "Manual") {
            ; Manual mode - always approve
            method := VerificationManager.METHOD_MANUAL
            success := true
            this.LogMessage("INFO", "Signature verification: Manual mode - auto-approved")
        } else {
            ; Auto or Both mode
            if (verifyResult.result && verifyResult.confidence >= confidenceThreshold) {
                method := VerificationManager.METHOD_AUTO
                success := true
                this.LogMessage("INFO", "Signature verification: Auto-approved with " verifyResult.confidence "% confidence")
            } else if (verificationMode == "Both") {
                ; In Both mode, approve even if below threshold
                method := VerificationManager.METHOD_MANUAL
                success := true
                this.LogMessage("INFO", "Signature verification: Both mode - manual approval despite low confidence (" verifyResult.confidence "%)")
            } else {
                ; Auto mode with low confidence - fail
                method := VerificationManager.METHOD_AUTO
                success := false
                this.LogMessage("INFO", "Signature verification: Auto-rejected with " verifyResult.confidence "% confidence (below " confidenceThreshold "% threshold)")
            }
        }

        ; Create comprehensive result object
        result := {
            success: success,
            confidence: Integer(verifyResult.confidence),
            method: method,
            message: verifyResult.message,
            referenceUsed: referenceSignaturePath,
            capturedPath: capturedSignaturePath,
            mode: this.currentMode,
            timestamp: FormatTime(, "yyyy-MM-dd HH:mm:ss")
        }

        ; Log detailed verification result
        this.LogVerificationResult(VerificationManager.TYPE_SIGNATURE, rollNumber, result)

        return result
    }

    /**
     * Get the appropriate reference signature path based on current mode
     * @param {String} rollNumber - The candidate's roll number
     * @return {String} Path to the reference signature
     */
    GetReferenceSignaturePath(rollNumber) {
        candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages")

        switch this.currentMode {
            case VerificationManager.MODE_PRE_EXAM:
                ; Pre-exam mode: compare against registered signature
                return PathManager.GetCandidateSignaturePath(rollNumber)

            case VerificationManager.MODE_POST_EXAM:
                ; Post-exam mode: compare against pre-exam captured signature
                return candidatesImgPath rollNumber "_captured_signature_pre.jpg"

            case VerificationManager.MODE_VERIFY:
                ; Future verify mode: compare against post-exam captured signature
                return candidatesImgPath rollNumber "_captured_signature_post.jpg"

            default:
                ; Default to registered signature
                return PathManager.GetCandidateSignaturePath(rollNumber)
        }
    }

    /**
     * Save captured signature with appropriate naming convention
     * @param {String} rollNumber - The candidate's roll number
     * @param {String} sourcePath - Path to the source signature file
     * @return {Object} Save result with success status and saved path
     */
    SaveCapturedSignature(rollNumber, sourcePath) {
        try {
            candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages", true)

            ; Generate filename based on current mode
            filename := rollNumber "_captured_signature_" this.currentMode ".jpg"
            savePath := candidatesImgPath filename

            ; Copy the file
            FileCopy(sourcePath, savePath, true)

            this.LogMessage("INFO", "Saved captured signature: " savePath)

            return {
                success: true,
                savedPath: savePath,
                filename: filename
            }
        } catch Error as e {
            this.LogMessage("ERROR", "Error saving captured signature: " e.Message)
            return {
                success: false,
                savedPath: "",
                filename: "",
                error: e.Message
            }
        }
    }

    /**
     * Log message with prefix
     * @param {String} level - Log level (INFO, WARNING, ERROR)
     * @param {String} message - The message to log
     */
    LogMessage(level, message) {
        if (IsObject(ErrorHandler)) {
            ErrorHandler.LogMessage(level, this.logPrefix ": " message)
        } else {
            OutputDebug(this.logPrefix ": " level ": " message)
        }
    }
}
